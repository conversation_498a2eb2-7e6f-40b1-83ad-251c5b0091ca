#!/usr/bin/env python3
"""
Test script to verify GPU optimizations are working correctly
"""

import numpy as np
import time
import sys
import os

# Add the current directory to the path so we can import from app2
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from app2 import (
        gpu_normalize_embeddings, 
        gpu_normalize_single_vector, 
        gpu_similarity_search,
        clean_text_parallel,
        GPU_AVAILABLE
    )
    print("✓ Successfully imported GPU optimization functions")
except ImportError as e:
    print(f"✗ Failed to import functions: {e}")
    sys.exit(1)

def test_gpu_normalization():
    """Test GPU-accelerated vector normalization"""
    print("\n=== Testing GPU Vector Normalization ===")
    
    # Create test embeddings
    test_embeddings = np.random.randn(1000, 512).astype(np.float32)
    test_vector = np.random.randn(512).astype(np.float32)
    
    # Test batch normalization
    print("Testing batch normalization...")
    start_time = time.time()
    normalized_batch = gpu_normalize_embeddings(test_embeddings)
    gpu_time = time.time() - start_time
    
    # CPU baseline
    start_time = time.time()
    cpu_normalized = test_embeddings / np.linalg.norm(test_embeddings, axis=1)[:, None]
    cpu_time = time.time() - start_time
    
    # Verify correctness
    diff = np.mean(np.abs(normalized_batch - cpu_normalized))
    print(f"  GPU time: {gpu_time:.4f}s")
    print(f"  CPU time: {cpu_time:.4f}s")
    print(f"  Speedup: {cpu_time/gpu_time:.2f}x" if gpu_time > 0 else "  Speedup: N/A")
    print(f"  Mean difference: {diff:.8f}")
    print(f"  ✓ Batch normalization {'PASSED' if diff < 1e-6 else 'FAILED'}")
    
    # Test single vector normalization
    print("\nTesting single vector normalization...")
    start_time = time.time()
    normalized_single = gpu_normalize_single_vector(test_vector)
    gpu_single_time = time.time() - start_time
    
    start_time = time.time()
    cpu_single = test_vector / np.linalg.norm(test_vector)
    cpu_single_time = time.time() - start_time
    
    single_diff = np.mean(np.abs(normalized_single - cpu_single))
    print(f"  GPU time: {gpu_single_time:.4f}s")
    print(f"  CPU time: {cpu_single_time:.4f}s")
    print(f"  Mean difference: {single_diff:.8f}")
    print(f"  ✓ Single vector normalization {'PASSED' if single_diff < 1e-6 else 'FAILED'}")

def test_gpu_similarity_search():
    """Test GPU-accelerated similarity search"""
    print("\n=== Testing GPU Similarity Search ===")
    
    # Create test data
    embeddings = np.random.randn(5000, 512).astype(np.float32)
    embeddings = embeddings / np.linalg.norm(embeddings, axis=1)[:, None]  # Normalize
    query = np.random.randn(512).astype(np.float32)
    query = query / np.linalg.norm(query)  # Normalize
    
    k = 10
    
    # GPU search
    print("Testing GPU similarity search...")
    start_time = time.time()
    gpu_scores, gpu_indices = gpu_similarity_search(query, embeddings, k)
    gpu_search_time = time.time() - start_time
    
    # CPU baseline
    start_time = time.time()
    similarities = np.dot(embeddings, query)
    top_k_indices = np.argpartition(similarities, -k)[-k:]
    top_k_indices = top_k_indices[np.argsort(similarities[top_k_indices])][::-1]
    cpu_scores = similarities[top_k_indices]
    cpu_search_time = time.time() - start_time
    
    # Verify correctness (scores should be very similar)
    score_diff = np.mean(np.abs(gpu_scores - cpu_scores))
    print(f"  GPU time: {gpu_search_time:.4f}s")
    print(f"  CPU time: {cpu_search_time:.4f}s")
    print(f"  Speedup: {cpu_search_time/gpu_search_time:.2f}x" if gpu_search_time > 0 else "  Speedup: N/A")
    print(f"  Score difference: {score_diff:.8f}")
    print(f"  ✓ Similarity search {'PASSED' if score_diff < 1e-5 else 'FAILED'}")

def test_parallel_text_processing():
    """Test parallel text processing"""
    print("\n=== Testing Parallel Text Processing ===")
    
    # Create test texts
    test_texts = [
        "This is a sample insurance document with various clauses and conditions.",
        "Coverage includes medical expenses up to $50,000 per incident.",
        "Exclusions apply to pre-existing conditions and experimental treatments.",
        "Deductible amount is $500 per claim for in-network providers.",
        "Premium payments are due monthly on the first of each month."
    ] * 20  # 100 texts total
    
    # Test parallel cleaning
    print("Testing parallel text cleaning...")
    start_time = time.time()
    parallel_cleaned = clean_text_parallel(test_texts)
    parallel_time = time.time() - start_time
    
    # Sequential baseline
    start_time = time.time()
    from app2 import clean_text
    sequential_cleaned = [clean_text(text) for text in test_texts]
    sequential_time = time.time() - start_time
    
    # Verify correctness
    texts_match = all(p == s for p, s in zip(parallel_cleaned, sequential_cleaned))
    print(f"  Parallel time: {parallel_time:.4f}s")
    print(f"  Sequential time: {sequential_time:.4f}s")
    print(f"  Speedup: {sequential_time/parallel_time:.2f}x" if parallel_time > 0 else "  Speedup: N/A")
    print(f"  ✓ Text cleaning {'PASSED' if texts_match else 'FAILED'}")

def main():
    """Run all tests"""
    print("GPU Optimization Test Suite")
    print("=" * 50)
    print(f"GPU Available: {GPU_AVAILABLE}")
    
    if not GPU_AVAILABLE:
        print("⚠️  GPU not available - tests will run CPU fallback versions")
    
    try:
        test_gpu_normalization()
        test_gpu_similarity_search()
        test_parallel_text_processing()
        
        print("\n" + "=" * 50)
        print("✓ All tests completed successfully!")
        print("\nGPU optimizations are working correctly.")
        
    except Exception as e:
        print(f"\n✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
